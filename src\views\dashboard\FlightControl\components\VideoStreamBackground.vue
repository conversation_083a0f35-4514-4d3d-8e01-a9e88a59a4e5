<script setup lang="ts">
// 视频流背景组件 - 飞控模式下的背景
</script>

<template>
  <div class="video-stream-background">
    <!-- 视频流容器 -->
    <div class="video-container">
      <!-- 暂时显示占位内容 -->
      <div class="video-placeholder">
        <!-- 中心准心 -->
        <div class="crosshair">
          <div class="crosshair-horizontal"></div>
          <div class="crosshair-vertical"></div>
        </div>
        <!-- 无信号提示 -->
        <div class="no-signal-text">暂无视频信号</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.video-stream-background {
  width: 100%;
  height: 100%;
  background: $bg-dark;
  position: relative;
  overflow: hidden;

  .video-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .video-placeholder {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      // 准心样式
      .crosshair {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 2rem;
        height: 2rem;

        .crosshair-horizontal,
        .crosshair-vertical {
          position: absolute;
          background: $primary-color;
          opacity: 0.8;
        }

        .crosshair-horizontal {
          top: 50%;
          left: 0;
          width: 100%;
          height: 1px;
          transform: translateY(-50%);
        }

        .crosshair-vertical {
          left: 50%;
          top: 0;
          width: 1px;
          height: 100%;
          transform: translateX(-50%);
        }
      }

      // 无信号文字
      .no-signal-text {
        position: absolute;
        bottom: 2rem;
        left: 50%;
        transform: translateX(-50%);
        color: $text-secondary;
        font-size: $font-size-panel-normal;
        text-align: center;
      }
    }
  }
}
</style>
