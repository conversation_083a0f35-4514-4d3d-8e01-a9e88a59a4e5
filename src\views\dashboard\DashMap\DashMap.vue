<template>
  <div id="mapContainer" />
  <!-- 测试按钮 -->
  <!-- <div class="absolute top-10 right-4 flex flex-col items-end gap-2 z-10 w-24">
      <el-button @click="flyToBeijing">定位承德</el-button>
      <el-button @click="drawRectangle">绘制矩形</el-button>
      <el-button @click="addMarker">添加标记</el-button>
      <el-button @click="toggleBaseMap">切换底图</el-button>
      <el-button @click="toggle2D3D">切换视图</el-button>
      <el-button @click="resetCamera">视角复位</el-button>
      <el-button @click="toggleFullscreen">全屏显示</el-button>
    </div> -->
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import * as mars3d from 'mars3d'

let map: mars3d.Map
let graphicLayer: mars3d.layer.GraphicLayer

onMounted(() => {
  // 设置容器高度
  const mapContainer = document.getElementById('mapContainer')
  if (mapContainer) {
    mapContainer.style.height = '100%'
    mapContainer.style.width = '100%'
  }

  // 创建三维地球场景
  map = new mars3d.Map('mapContainer', {
    scene: {
      // 默认视角参数
      center: { lat: 28.9631, lng: 118.8743, alt: 175000, heading: 0, pitch: -90 }, // 衢州市
      shadows: false, // 是否启用日照阴影
      removeDblClick: true, // 是否移除Cesium默认的双击事件

      // 以下是Cesium.Viewer所支持的options【控件相关的写在另外的control属性中】
      sceneMode: 3, // 3等价于Cesium.SceneMode.SCENE3D,

      // 以下是Cesium.Scene对象相关参数
      showSun: true, // 是否显示太阳
      showMoon: true, // 是否显示月亮
      showSkyBox: true, // 是否显示天空盒
      showSkyAtmosphere: true, // 是否显示地球大气层外光圈
      fog: true, // 是否启用雾化效果
      fxaa: true, // 是否启用抗锯齿

      // 以下是Cesium.Globe对象相关参数
      globe: {
        depthTestAgainstTerrain: false, // 是否启用深度监测
        baseColor: '#546a53', // 地球默认背景色
        showGroundAtmosphere: true, // 是否在地球上绘制的地面大气
        enableLighting: false, // 是否显示昼夜区域
      },
      // 以下是Cesium.ScreenSpaceCameraController对象相关参数
      cameraController: {
        zoomFactor: 3.0, // 鼠标滚轮放大的步长参数
        minimumZoomDistance: 1, // 地球放大的最小值（以米为单位）
        maximumZoomDistance: 50000000, // 地球缩小的最大值（以米为单位）
        enableRotate: true, // 2D和3D视图下，是否允许用户旋转相机
        enableTranslate: true, // 2D和哥伦布视图下，是否允许用户平移地图
        enableTilt: true, // 3D和哥伦布视图下，是否允许用户倾斜相机
        enableZoom: true, // 是否允许 用户放大和缩小视图
        enableCollisionDetection: true, // 是否允许 地形相机的碰撞检测
      },
    },
    control: {
      baseLayerPicker: false,
      homeButton: false,
      sceneModePicker: false,
      navigationHelpButton: false,
      fullscreenButton: false,
    },

    // terrain: {
    //   url: 'http://data.mars3d.cn/terrain',
    //   show: true,
    // },

    basemaps: [
      {
        type: 'gaode',
        layer: 'img_d', // img_d: 影像底图
        show: true,
      } as mars3d.Map.basemapOptions,
    ],

    layers: [
      {
        type: 'gaode',
        layer: 'img_z', // img_z: 影像标注
        show: true,
      } as mars3d.Map.layerOptions,
    ],
  })

  // 创建矢量数据图层
  graphicLayer = new mars3d.layer.GraphicLayer()
  map.addLayer(graphicLayer)
})

// 定位到北京
const flyToBeijing = () => {
  // 飞行定位
  map.setCameraView({
    lat: 40.9515,
    lng: 117.9634,
    alt: 500000,
    heading: 0,
    pitch: -90,
  })
}

// 绘制矩形
const drawRectangle = () => {
  // 清除之前的绘制
  graphicLayer.clear()

  // 开启矩形绘制
  graphicLayer.startDraw({
    type: 'rectangle',
    style: {
      color: '#ff0000',
      opacity: 0.3,
      outline: true,
      outlineColor: '#ffffff',
    },
    success: (graphic: any) => {
      const rectangle = graphic.getGeometry()
      console.log('矩形坐标:', rectangle)
    },
  })
}

// 添加标记点
const addMarker = () => {
  const marker = new mars3d.graphic.PointEntity({
    position: [117.9634, 40.9515],
    style: {
      color: '#ff0000',
      pixelSize: 10,
      outline: true,
      label: {
        text: '我的标记',
        font_size: 20,
        color: '#ffffff',
        pixelOffsetY: -20,
      },
    },
  })
  graphicLayer.addGraphic(marker)
}

// 切换底图
const toggleBaseMap = () => {
  const basemaps = map.getBasemaps()
  basemaps.forEach((basemap) => {
    basemap.show = !basemap.show
  })
}

// 切换 2D/3D
const toggle2D3D = () => {
  const scene = map.scene
  // 3 = SCENE3D, 2 = SCENE2D
  if (scene.mode === 3) {
    scene.mode = 2
  } else {
    scene.mode = 3
  }
}

// 视角复位
const resetCamera = () => {
  map.setCameraView({
    lat: 40.9515,
    lng: 117.9634,
    alt: 1500000,
    heading: 0,
    pitch: -90,
  })
}

// 全屏显示
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}
</script>

<style scoped lang="scss">
#mapContainer {
  width: 100%;
  height: 100%;
}
</style>
