<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useUIStore } from '@/stores/uiStore'
import { useDroneStore } from '@/stores/droneStore'
import DashHeader from './DashHeader.vue'
import LeftPanel from './LeftPanel/LeftPanel.vue'
import RightPanel from './RightPanel/RightPanel.vue'
import BottomPanel from './BottomPanel/BottomPanel.vue'
import DashMap from './DashMap/DashMap.vue'
// 飞控相关组件
import MonitorCards from './FlightControl/components/MonitorCards.vue'
import TopFlightStatusBar from './FlightControl/components/TopFlightStatusBar.vue'
import LeftFlightPanel from './FlightControl/LeftFlightPanel/LeftFlightPanel.vue'
import RightFlightPanel from './FlightControl/RightFlightPanel/RightFlightPanel.vue'
import VideoStreamBackground from './FlightControl/components/VideoStreamBackground.vue'

// 使用Store
const uiStore = useUIStore()
const droneStore = useDroneStore()

// 当前视图模式
const viewMode = computed(() => uiStore.viewMode)
// 监控卡片显示状态
const showMonitorCards = computed(() => uiStore.showMonitorCards || viewMode.value === 'flight')

// 组件挂载时获取基础数据
onMounted(() => {
  // 获取无人机列表
  droneStore.fetchDroneList()
})
</script>
<template>
  <div class="dashboard-container">
    <!-- 背景层：地图和视频流 -->
    <div class="background-layer">
      <!-- 背景图片 -->
      <div class="background-overlay">
        <img src="@/assets/images/mask_border.png" alt="Background" />
      </div>

      <!-- 地图背景 - 正常模式显示  组件开发期间不渲染地图 占内存太大-->
      <DashMap v-if="viewMode !== 'flight'" class="map-background" />

      <!-- 视频流背景 - 飞控模式显示 -->
      <VideoStreamBackground v-show="viewMode === 'flight'" class="video-background" />
    </div>

    <!-- UI组件层：所有UI组件都使用绝对定位 -->
    <div class="ui-components-layer">
      <!-- 头部组件 -->
      <div class="dashboard-header">
        <DashHeader />
      </div>

      <!-- 常规面板 - 正常模式显示 -->
      <template v-if="viewMode !== 'flight'">
        <div class="dashboard-left-panel">
          <LeftPanel />
        </div>
        <div class="dashboard-right-panel">
          <RightPanel />
        </div>
        <div class="dashboard-bottom-panel">
          <BottomPanel />
        </div>
      </template>

      <!-- 飞控面板 - 飞控模式显示 -->
      <template v-if="viewMode === 'flight'">
        <div class="flight-top-bar">
          <TopFlightStatusBar />
        </div>
        <div class="flight-left-panel">
          <LeftFlightPanel />
        </div>
        <div class="flight-right-panel">
          <RightFlightPanel />
        </div>
      </template>

      <!-- 监控卡片 - 显示状态控制 -->
      <div v-if="showMonitorCards" class="monitor-cards">
        <MonitorCards />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.dashboard-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  // 背景层 - 最底层，z-index: 1
  .background-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;

    .background-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      pointer-events: none;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        opacity: 0.6;
        mix-blend-mode: overlay;
      }
    }

    .map-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2;
    }

    .video-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2;
    }
  }

  // UI组件层 - 所有组件都使用绝对定位，z-index: 10+
  .ui-components-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    pointer-events: none; // 让点击穿透到地图，除非组件内部设置pointer-events: auto
  }

  // 头部组件
  .dashboard-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: $dashboard-header-height;
    pointer-events: auto;
  }

  // 常规面板
  .dashboard-left-panel {
    position: absolute;
    top: calc($dashboard-header-height + $dashboard-panel-margin); // 头部高度 + 间距
    bottom: $dashboard-panel-margin;
    left: $dashboard-panel-margin;
    width: $dashboard-panel-width;
    pointer-events: auto;
  }

  .dashboard-right-panel {
    position: absolute;
    top: calc($dashboard-header-height + $dashboard-panel-margin); // 头部高度 + 间距
    bottom: $dashboard-panel-margin;
    right: $dashboard-panel-margin;
    width: $dashboard-panel-width;
    pointer-events: auto;
  }

  .dashboard-bottom-panel {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: auto;
  }

  // 飞控面板
  .flight-top-bar {
    position: absolute;
    top: calc($dashboard-header-height + $dashboard-panel-margin + 1.5rem); // 头部高度 + 间距
    left: 50%;
    transform: translateX(-50%);
    pointer-events: auto;
  }

  .flight-left-panel {
    position: absolute;
    top: calc($dashboard-header-height + $dashboard-panel-margin * 2); // 头部  + 间距
    bottom: $dashboard-panel-margin;
    left: $dashboard-panel-margin; // 与常规左侧面板保持一致
    width: $dashboard-panel-width;
    pointer-events: auto;
  }

  .flight-right-panel {
    position: absolute;
    top: calc($dashboard-header-height + $dashboard-panel-margin * 2); // 头部  + 间距
    bottom: $dashboard-panel-margin;
    right: $dashboard-panel-margin; // 与常规右侧面板保持一致
    width: $dashboard-panel-width;
    pointer-events: auto;
  }

  // 监控卡片 - 修复定位问题
  .monitor-cards {
    position: absolute;
    bottom: $dashboard-panel-margin; // 与底部保持间距
    right: calc(
      $dashboard-panel-width + $dashboard-panel-margin * 2
    ); // 右侧面板宽度 + 右侧面板右间距
    pointer-events: auto;
    z-index: 20; // 确保在其他组件之上

    // 确保卡片容器不受父容器影响
    width: $monitor-card-width;
    height: auto;
  }
}
</style>
