<script setup lang="ts">
import { computed } from 'vue'
import { useUIStore } from '@/stores/uiStore'

// 使用UI Store
const uiStore = useUIStore()

// 当前视图模式
const viewMode = computed(() => uiStore.viewMode)

// 监控卡片数据
const monitorCards = ref([
  {
    id: 'main',
    title: uiStore.viewMode === 'flight' ? '地图视图' : '主监控',
    type: 'video', // 正常模式下显示视频，飞控模式下显示地图
  },
  {
    id: 'secondary',
    title: '辅助监控',
    type: 'video',
  },
])

// 处理卡片点击事件
const handleCardClick = () => {
  if (uiStore.viewMode !== 'flight') {
    // 进入飞控模式，会自动获取数据
    uiStore.enterFlightMode()
  }
}
</script>

<template>
  <div class="monitor-cards-1">
    <div class="cards-container">
      <div
        v-for="card in monitorCards"
        :key="card.id"
        class="monitor-card"
        @click="handleCardClick"
      >
        <!-- 卡片头部 -->
        <div class="card-header">
          <span class="card-title">{{ card.title }}</span>
        </div>

        <!-- 卡片内容 -->
        <div class="card-content">
          <!-- 第一个卡片：正常模式显示视频，飞控模式显示地图 -->
          <div v-if="card.id === 'main'" class="content-area">
            <div v-if="viewMode === 'flight'" class="mini-map">
              <!-- 小地图占位 -->
              <div class="map-placeholder">
                <UIcon name="mdi:map" size="2rem" color="#00FFFE" />
                <span>地图视图</span>
              </div>
            </div>
            <div v-else class="video-stream">
              <!-- 视频流占位 -->
              <div class="video-placeholder">
                <UIcon name="mdi:video" size="2rem" color="#00FFFE" />
                <span>视频流</span>
              </div>
            </div>
          </div>

          <!-- 其他卡片：始终显示视频 -->
          <div v-else class="content-area">
            <div class="video-stream">
              <div class="video-placeholder">
                <UIcon name="mdi:video" size="2rem" color="#00FFFE" />
                <span>视频流</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.monitor-cards-1 {
  display: flex;
  flex-direction: column;
  // gap: $monitor-card-gap;

  .cards-container {
    display: flex;
    flex-direction: column;
    // gap: $monitor-card-gap;
  }

  .monitor-card {
    width: $monitor-card-width;
    height: $monitor-card-height;
    background: $bg-panel;
    border-radius: $border-radius-base;
    box-shadow: inset 0 0 1rem rgba($glow-color, 0.3);
    overflow: hidden;
    display: flex;
    flex-direction: column;

    &:hover {
      cursor: pointer;
    }

    .card-header {
      height: 2rem;
      background: rgba($primary-color, 0.1);
      border-bottom: 1px solid rgba($primary-color, 0.2);
      display: flex;
      align-items: center;
      padding: 0 0.75rem;

      .card-title {
        font-size: $font-size-panel-label;
        color: $text-active;
        font-weight: bold;
      }
    }

    .card-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .content-area {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .mini-map,
        .video-stream {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;

          .map-placeholder,
          .video-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            color: $text-secondary;

            span {
              font-size: $font-size-panel-caption;
            }
          }
        }
      }
    }
  }
}
</style>
